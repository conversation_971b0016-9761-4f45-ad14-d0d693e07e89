const fs = require('fs');
const path = require('path');
const axios = require('axios');
const config = require('./config');
const { loadDrChronoToken } = require('./utils/token-manager');
const GoHighLevelClient = require('./utils/gohighlevel-client');
const { mapPatientToContact, validatePatient } = require('./utils/patient-mapper');

// Create data directory if it doesn't exist
const dataDir = config.paths.dataDir;
if (!fs.existsSync(dataDir)) {
  fs.mkdirSync(dataDir, { recursive: true });
}

class PatientSyncService {
  constructor() {
    this.drchronoBaseUrl = config.drchrono.baseUrl;
    this.accessToken = null;
    this.ghlClient = null;
    this.syncStats = {
      total: 0,
      created: 0,
      updated: 0,
      skipped: 0,
      errors: 0
    };
  }

  async initialize() {
    try {
      // Load DrChrono access token
      this.accessToken = await loadDrChronoToken();
      
      // Initialize GoHighLevel client
      const ghlApiKey = process.env.GOHIGHLEVEL_API_KEY;
      if (!ghlApiKey) {
        throw new Error('GOHIGHLEVEL_API_KEY environment variable is required');
      }
      
      this.ghlClient = new GoHighLevelClient(ghlApiKey);
      
      console.log('Patient sync service initialized successfully');
    } catch (error) {
      console.error('Failed to initialize sync service:', error.message);
      throw error;
    }
  }

  // Fetch patients from DrChrono API with pagination
  async fetchPatientsFromDrChrono(since = null) {
    const patients = [];
    let nextUrl = `${this.drchronoBaseUrl}/patients`;
    
    // Add since parameter for incremental sync
    if (since) {
      nextUrl += `?since=${since}`;
    }

    const headers = {
      'Authorization': `Bearer ${this.accessToken}`,
      'Content-Type': 'application/json'
    };

    try {
      while (nextUrl) {
        console.log(`Fetching patients from: ${nextUrl}`);
        
        const response = await axios.get(nextUrl, { headers });
        const data = response.data;
        
        if (data.results && Array.isArray(data.results)) {
          patients.push(...data.results);
          console.log(`Fetched ${data.results.length} patients. Total so far: ${patients.length}`);
        }
        
        // Get next page URL
        nextUrl = data.next;
        
        // Add delay between requests to respect rate limits
        if (nextUrl) {
          await this.delay(config.sync.delayBetweenRequests);
        }
      }
      
      console.log(`Total patients fetched from DrChrono: ${patients.length}`);
      return patients;
      
    } catch (error) {
      console.error('Error fetching patients from DrChrono:', error.response?.data || error.message);
      throw error;
    }
  }

  // Sync a single patient to GoHighLevel
  async syncPatientToGoHighLevel(patient) {
    try {
      // Validate patient data
      const validation = validatePatient(patient);
      if (!validation.isValid) {
        console.warn(`Skipping invalid patient ${patient.id}:`, validation.errors);
        this.syncStats.skipped++;
        return;
      }

      // Map patient data to GoHighLevel contact format
      const contactData = mapPatientToContact(patient);
      
      // Search for existing contact
      const existingContacts = await this.ghlClient.searchContact(
        contactData.email, 
        contactData.phone
      );
      
      if (existingContacts.length > 0) {
        // Update existing contact
        const existingContact = existingContacts[0];
        await this.ghlClient.updateContact(existingContact.id, contactData);
        this.syncStats.updated++;
        console.log(`Updated existing contact: ${existingContact.id}`);
      } else {
        // Create new contact
        await this.ghlClient.createContact(contactData);
        this.syncStats.created++;
        console.log(`Created new contact for patient: ${patient.id}`);
      }
      
    } catch (error) {
      console.error(`Error syncing patient ${patient.id}:`, error.message);
      this.syncStats.errors++;
    }
  }

  // Main sync function
  async syncPatients(since = null) {
    try {
      console.log('Starting patient sync...');
      console.log('='.repeat(50));
      
      await this.initialize();
      
      // Fetch patients from DrChrono
      const patients = await this.fetchPatientsFromDrChrono(since);
      this.syncStats.total = patients.length;
      
      if (patients.length === 0) {
        console.log('No patients to sync.');
        return;
      }
      
      // Process patients in batches
      const batchSize = config.sync.batchSize;
      for (let i = 0; i < patients.length; i += batchSize) {
        const batch = patients.slice(i, i + batchSize);
        console.log(`Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(patients.length / batchSize)}`);
        
        // Process batch concurrently but with limited concurrency
        const promises = batch.map(patient => this.syncPatientToGoHighLevel(patient));
        await Promise.allSettled(promises);
        
        // Add delay between batches
        if (i + batchSize < patients.length) {
          await this.delay(config.sync.delayBetweenRequests);
        }
      }
      
      // Print sync statistics
      this.printSyncStats();
      
    } catch (error) {
      console.error('Sync failed:', error.message);
      throw error;
    }
  }

  // Utility function to add delay
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Print sync statistics
  printSyncStats() {
    console.log('\n' + '='.repeat(50));
    console.log('SYNC COMPLETED');
    console.log('='.repeat(50));
    console.log(`Total patients processed: ${this.syncStats.total}`);
    console.log(`Created: ${this.syncStats.created}`);
    console.log(`Updated: ${this.syncStats.updated}`);
    console.log(`Skipped: ${this.syncStats.skipped}`);
    console.log(`Errors: ${this.syncStats.errors}`);
    console.log('='.repeat(50));
  }
}

// Main execution
async function main() {
  try {
    const syncService = new PatientSyncService();
    
    // Get since parameter from command line arguments
    const since = process.argv[2]; // Format: YYYY-MM-DD or ISO string
    
    if (since) {
      console.log(`Syncing patients since: ${since}`);
    } else {
      console.log('Syncing all patients (full sync)');
    }
    
    await syncService.syncPatients(since);
    
  } catch (error) {
    console.error('Application failed:', error.message);
    process.exit(1);
  }
}

// Run the sync if this file is executed directly
if (require.main === module) {
  main();
}

module.exports = PatientSyncService;
