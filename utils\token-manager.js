const fs = require('fs');
const axios = require('axios');
const config = require('../config');

const tokenFilePath = config.paths.tokenFile;

// Function to load and validate the DrChrono access token
async function loadDrChronoToken() {
  try {
    // Read the token file
    const tokenData = JSON.parse(fs.readFileSync(tokenFilePath, 'utf8'));
    let accessToken = tokenData.access_token;

    // Check if token exists
    if (!accessToken) {
      console.error('Error: Invalid token file. No access token found.');
      process.exit(1);
    }

    // Check if token is expired or about to expire (within 5 minutes)
    const expiryTime = new Date(tokenData.expiry_time);
    const now = new Date();
    const bufferTime = 5 * 60 * 1000; // 5 minutes in milliseconds

    if (expiryTime < new Date(now.getTime() + bufferTime)) {
      console.log('Access token is expired or about to expire. Refreshing...');
      accessToken = await refreshDr<PERSON>hronoToken();
    } else {
      console.log('Token is valid until:', expiryTime.toISOString());
    }

    console.log('Token loaded successfully.');
    return accessToken;
  } catch (error) {
    console.error('Error loading token file:', error.message);
    process.exit(1);
  }
}

// Function to refresh the DrChrono access token
async function refreshDrChronoToken() {
  try {
    const tokenData = JSON.parse(fs.readFileSync(tokenFilePath, 'utf8'));
    
    if (!tokenData.refresh_token) {
      console.error('Error: No refresh token found. Please re-authenticate.');
      process.exit(1);
    }

    console.log('Refreshing access token...');
    
    // Note: You'll need to add your client_id and client_secret
    const response = await axios.post('https://drchrono.com/o/token/', {
      grant_type: 'refresh_token',
      refresh_token: tokenData.refresh_token,
      client_id: process.env.DRCHRONO_CLIENT_ID,
      client_secret: process.env.DRCHRONO_CLIENT_SECRET
    });

    const newTokenData = {
      access_token: response.data.access_token,
      refresh_token: response.data.refresh_token || tokenData.refresh_token,
      expires_in: response.data.expires_in,
      token_type: response.data.token_type,
      expiry_time: new Date(Date.now() + response.data.expires_in * 1000).toISOString()
    };

    // Save the new token data
    fs.writeFileSync(tokenFilePath, JSON.stringify(newTokenData, null, 2));
    console.log('Token refreshed successfully.');
    
    return newTokenData.access_token;
  } catch (error) {
    console.error('Error refreshing token:', error.response?.data || error.message);
    process.exit(1);
  }
}

module.exports = {
  loadDrChronoToken,
  refreshDrChronoToken
};
