const axios = require('axios');
const config = require('../config');

class GoHighLevelClient {
  constructor(apiKey) {
    this.apiKey = apiKey;
    this.baseUrl = config.gohighlevel.baseUrl;
    this.client = axios.create({
      baseURL: this.baseUrl,
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      }
    });
  }

  // Create a new contact in GoHighLevel
  async createContact(contactData) {
    try {
      console.log('Creating contact in GoHighLevel:', contactData.email || contactData.phone);
      
      const response = await this.client.post('/contacts', contactData);
      console.log('Contact created successfully:', response.data.contact?.id);
      
      return response.data;
    } catch (error) {
      console.error('Error creating contact:', error.response?.data || error.message);
      throw error;
    }
  }

  // Update an existing contact in GoHighLevel
  async updateContact(contactId, contactData) {
    try {
      console.log('Updating contact in GoHighLevel:', contactId);
      
      const response = await this.client.put(`/contacts/${contactId}`, contactData);
      console.log('Contact updated successfully:', contactId);
      
      return response.data;
    } catch (error) {
      console.error('Error updating contact:', error.response?.data || error.message);
      throw error;
    }
  }

  // Search for existing contact by email or phone
  async searchContact(email, phone) {
    try {
      const searchParams = new URLSearchParams();
      if (email) searchParams.append('email', email);
      if (phone) searchParams.append('phone', phone);

      const response = await this.client.get(`/contacts/search?${searchParams}`);
      return response.data.contacts || [];
    } catch (error) {
      console.error('Error searching contact:', error.response?.data || error.message);
      return [];
    }
  }

  // Get contact by ID
  async getContact(contactId) {
    try {
      const response = await this.client.get(`/contacts/${contactId}`);
      return response.data.contact;
    } catch (error) {
      if (error.response?.status === 404) {
        return null;
      }
      console.error('Error getting contact:', error.response?.data || error.message);
      throw error;
    }
  }
}

module.exports = GoHighLevelClient;
