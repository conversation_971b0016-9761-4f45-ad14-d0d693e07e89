// Map DrChrono patient data to GoHighLevel contact format
function mapPatientToContact(patient) {
  const contact = {
    firstName: patient.first_name || '',
    lastName: patient.last_name || '',
    email: patient.email || '',
    phone: patient.cell_phone || patient.home_phone || patient.office_phone || '',
    address1: patient.address || '',
    city: patient.city || '',
    state: patient.state || '',
    postalCode: patient.zip_code || '',
    country: 'US', // Default to US, adjust as needed
    source: 'DrChrono',
    tags: ['DrChrono Patient'],
    customFields: {
      drchrono_id: patient.id?.toString(),
      date_of_birth: patient.date_of_birth || '',
      gender: patient.gender || '',
      social_security_number: patient.social_security_number || '',
      emergency_contact_name: patient.emergency_contact_name || '',
      emergency_contact_phone: patient.emergency_contact_phone || '',
      patient_status: patient.patient_status || '',
      chart_id: patient.chart_id || '',
      doctor: patient.doctor?.toString() || '',
      primary_care_physician: patient.primary_care_physician || '',
      patient_photo: patient.patient_photo || '',
      ethnicity: patient.ethnicity || '',
      race: patient.race || '',
      preferred_language: patient.preferred_language || '',
      patient_flags: patient.patient_flags ? patient.patient_flags.join(', ') : '',
      copay: patient.copay?.toString() || '',
      patient_payment_profile: patient.patient_payment_profile || ''
    }
  };

  // Clean up empty fields
  Object.keys(contact).forEach(key => {
    if (contact[key] === '' || contact[key] === null || contact[key] === undefined) {
      delete contact[key];
    }
  });

  // Clean up empty custom fields
  Object.keys(contact.customFields || {}).forEach(key => {
    if (contact.customFields[key] === '' || contact.customFields[key] === null || contact.customFields[key] === undefined) {
      delete contact.customFields[key];
    }
  });

  return contact;
}

// Validate that patient has minimum required data
function validatePatient(patient) {
  const errors = [];

  if (!patient.first_name && !patient.last_name) {
    errors.push('Patient must have at least first name or last name');
  }

  if (!patient.email && !patient.cell_phone && !patient.home_phone && !patient.office_phone) {
    errors.push('Patient must have at least email or phone number');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

module.exports = {
  mapPatientToContact,
  validatePatient
};
