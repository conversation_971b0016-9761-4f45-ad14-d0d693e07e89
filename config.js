const path = require('path');

module.exports = {
  // DrChrono API Configuration
  drchrono: {
    baseUrl: 'https://app.drchrono.com/api',
    endpoints: {
      patients: '/patients',
      patient: '/patients/{id}'
    }
  },

  // GoHighLevel API Configuration
  gohighlevel: {
    baseUrl: 'https://rest.gohighlevel.com/v1',
    endpoints: {
      contacts: '/contacts',
      contact: '/contacts/{id}'
    }
  },

  // File paths
  paths: {
    tokenFile: path.join(__dirname, 'data', 'drchrono-token.json'),
    dataDir: path.join(__dirname, 'data')
  },

  // Sync settings
  sync: {
    batchSize: 100, // Number of patients to process at once
    delayBetweenRequests: 1000, // Delay in milliseconds between API calls
    maxRetries: 3
  }
};
