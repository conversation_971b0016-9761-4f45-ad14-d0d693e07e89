# DrChrono to GoHighLevel Patient Sync

This application syncs patient data from DrChrono EHR to GoHighLevel CRM.

## Features

- Fetches patients from DrChrono API with pagination support
- Maps patient data to GoHighLevel contact format
- Handles both full sync and incremental sync (since a specific date)
- Automatic token refresh for DrChrono API
- Batch processing with rate limiting
- Comprehensive error handling and logging
- Duplicate detection and contact updates

## Setup

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Configure environment variables:**
   ```bash
   cp .env.example .env
   ```
   
   Edit `.env` and add your API credentials:
   - `DRCHRONO_CLIENT_ID`: Your DrChrono OAuth client ID
   - `DRCHRONO_CLIENT_SECRET`: Your DrChrono OAuth client secret
   - `GOHIGHLEVEL_API_KEY`: Your GoHighLevel API key

3. **Ensure DrChrono token is available:**
   Make sure you have a valid `data/drchrono-token.json` file with your access token.

## Usage

### Full Sync (All Patients)
```bash
npm start
# or
node sync-patients.js
```

### Incremental Sync (Patients since a specific date)
```bash
node sync-patients.js 2024-01-01
# or
node sync-patients.js 2024-01-01T00:00:00Z
```

## Patient Data Mapping

The following DrChrono patient fields are mapped to GoHighLevel:

### Basic Contact Info
- `first_name` → `firstName`
- `last_name` → `lastName`
- `email` → `email`
- `cell_phone/home_phone/office_phone` → `phone`
- `address` → `address1`
- `city` → `city`
- `state` → `state`
- `zip_code` → `postalCode`

### Custom Fields
- `drchrono_id`: Original DrChrono patient ID
- `date_of_birth`: Patient's date of birth
- `gender`: Patient's gender
- `chart_id`: DrChrono chart ID
- `doctor`: Assigned doctor ID
- `patient_status`: Current patient status
- And many more medical-specific fields

## Configuration

Edit `config.js` to customize:
- API endpoints
- Batch sizes
- Rate limiting delays
- File paths

## Error Handling

The application includes comprehensive error handling:
- Invalid patient data is skipped with warnings
- API errors are logged and don't stop the entire sync
- Token refresh is automatic when needed
- Detailed sync statistics are provided

## Logging

The application provides detailed logging including:
- Progress updates during sync
- Individual patient processing status
- Final sync statistics (created, updated, skipped, errors)
- Error details for troubleshooting

## File Structure

```
├── sync-patients.js          # Main sync application
├── config.js                 # Configuration settings
├── utils/
│   ├── token-manager.js      # DrChrono token management
│   ├── gohighlevel-client.js # GoHighLevel API client
│   └── patient-mapper.js     # Data mapping utilities
├── data/
│   └── drchrono-token.json   # DrChrono access token
├── package.json              # Dependencies
└── README.md                 # This file
```

## API Requirements

### DrChrono API
- Valid OAuth access token with `patients:read` scope
- Rate limiting: Respects API rate limits with delays

### GoHighLevel API
- Valid API key with contact management permissions
- Supports contact creation and updates

## Troubleshooting

1. **Token expired errors**: The application automatically refreshes tokens, but ensure your refresh token is valid.

2. **Missing environment variables**: Make sure all required environment variables are set in your `.env` file.

3. **API rate limits**: The application includes built-in delays, but you can adjust them in `config.js`.

4. **Invalid patient data**: Patients without required fields (name and contact info) are skipped with warnings.
