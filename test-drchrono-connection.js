const axios = require('axios');
const { loadDrChronoToken } = require('./utils/token-manager');

async function testDrChronoConnection() {
  try {
    console.log('Testing DrChrono API connection...');
    
    // Load access token
    const accessToken = await loadDrChronoToken();
    
    // Test API connection with a simple request
    const response = await axios.get('https://app.drchrono.com/api/patients', {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json'
      },
      params: {
        page_size: 1 // Just get 1 patient to test connection
      }
    });
    
    console.log('✅ DrChrono API connection successful!');
    console.log(`Found ${response.data.count} total patients`);
    
    if (response.data.results && response.data.results.length > 0) {
      const patient = response.data.results[0];
      console.log('Sample patient data:');
      console.log(`- ID: ${patient.id}`);
      console.log(`- Name: ${patient.first_name} ${patient.last_name}`);
      console.log(`- Email: ${patient.email || 'N/A'}`);
      console.log(`- Phone: ${patient.cell_phone || patient.home_phone || 'N/A'}`);
    }
    
  } catch (error) {
    console.error('❌ DrChrono API connection failed:');
    console.error('Status:', error.response?.status);
    console.error('Error:', error.response?.data || error.message);
  }
}

// Run the test
testDrChronoConnection();
