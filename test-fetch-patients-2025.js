const axios = require('axios');
const fs = require('fs');
const path = require('path');
const { loadDrChronoToken } = require('./utils/token-manager');

async function fetchPatientsFrom2025() {
  try {
    console.log('Fetching patients from DrChrono since January 1, 2025...');
    console.log('='.repeat(60));
    
    // Load access token
    const accessToken = await loadDrChronoToken();
    
    // Set up the API request
    const baseUrl = 'https://app.drchrono.com/api/patients';
    const since = '2025-01-01'; // January 1, 2025
    
    let allPatients = [];
    let nextUrl = `${baseUrl}?since=${since}`;
    let pageCount = 0;
    
    const headers = {
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': 'application/json'
    };
    
    // Fetch all pages
    while (nextUrl) {
      pageCount++;
      console.log(`\nFetching page ${pageCount}...`);
      console.log(`URL: ${nextUrl}`);
      
      const response = await axios.get(nextUrl, { headers });
      const data = response.data;
      
      console.log(`Page ${pageCount} results:`);
      console.log(`- Patients in this page: ${data.results ? data.results.length : 0}`);
      console.log(`- Total count: ${data.count || 'N/A'}`);
      console.log(`- Next page: ${data.next ? 'Yes' : 'No'}`);
      
      if (data.results && Array.isArray(data.results)) {
        allPatients.push(...data.results);
        
        // Log details of each patient in this page
        data.results.forEach((patient, index) => {
          console.log(`\n  Patient ${index + 1}:`);
          console.log(`    ID: ${patient.id}`);
          console.log(`    Name: ${patient.first_name || 'N/A'} ${patient.last_name || 'N/A'}`);
          console.log(`    Email: ${patient.email || 'N/A'}`);
          console.log(`    Phone: ${patient.cell_phone || patient.home_phone || patient.office_phone || 'N/A'}`);
          console.log(`    Date of Birth: ${patient.date_of_birth || 'N/A'}`);
          console.log(`    Gender: ${patient.gender || 'N/A'}`);
          console.log(`    Address: ${patient.address || 'N/A'}`);
          console.log(`    City: ${patient.city || 'N/A'}, ${patient.state || 'N/A'} ${patient.zip_code || 'N/A'}`);
          console.log(`    Chart ID: ${patient.chart_id || 'N/A'}`);
          console.log(`    Doctor ID: ${patient.doctor || 'N/A'}`);
          console.log(`    Patient Status: ${patient.patient_status || 'N/A'}`);
          console.log(`    Created: ${patient.created_at || 'N/A'}`);
          console.log(`    Updated: ${patient.updated_at || 'N/A'}`);
        });
      }
      
      // Get next page URL
      nextUrl = data.next;
      
      // Add a small delay between requests to be respectful to the API
      if (nextUrl) {
        console.log('\nWaiting 1 second before next request...');
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
    
    // Summary
    console.log('\n' + '='.repeat(60));
    console.log('FETCH SUMMARY');
    console.log('='.repeat(60));
    console.log(`Total pages fetched: ${pageCount}`);
    console.log(`Total patients since Jan 1, 2025: ${allPatients.length}`);
    
    if (allPatients.length > 0) {
      console.log('\nFirst patient details:');
      const firstPatient = allPatients[0];
      console.log(JSON.stringify(firstPatient, null, 2));

      console.log('\nAll patient IDs:');
      allPatients.forEach((patient, index) => {
        console.log(`${index + 1}. ID: ${patient.id}, Name: ${patient.first_name} ${patient.last_name}`);
      });

      // Save all patient data to JSON file
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `patients-2025-${timestamp}.json`;
      const filepath = path.join(__dirname, 'data', filename);

      // Ensure data directory exists
      const dataDir = path.join(__dirname, 'data');
      if (!fs.existsSync(dataDir)) {
        fs.mkdirSync(dataDir, { recursive: true });
      }

      const outputData = {
        fetchDate: new Date().toISOString(),
        sinceDate: since,
        totalPatients: allPatients.length,
        totalPages: pageCount,
        patients: allPatients
      };

      try {
        fs.writeFileSync(filepath, JSON.stringify(outputData, null, 2));
        console.log(`\n💾 Patient data saved to: ${filename}`);
        console.log(`📁 Full path: ${filepath}`);
        console.log(`📊 File size: ${(fs.statSync(filepath).size / 1024).toFixed(2)} KB`);

        console.log('\n📋 JSON file structure:');
        console.log('  - fetchDate: When this data was retrieved');
        console.log('  - sinceDate: Filter date (2025-01-01)');
        console.log('  - totalPatients: Number of patients found');
        console.log('  - totalPages: Number of API pages processed');
        console.log('  - patients: Array of patient objects with full DrChrono data');

        console.log('\n🔍 Sample patient fields available:');
        if (allPatients.length > 0) {
          const sampleFields = Object.keys(allPatients[0]).slice(0, 10);
          sampleFields.forEach(field => {
            console.log(`  - ${field}: ${allPatients[0][field] || 'null'}`);
          });
          if (Object.keys(allPatients[0]).length > 10) {
            console.log(`  ... and ${Object.keys(allPatients[0]).length - 10} more fields`);
          }
        }
      } catch (writeError) {
        console.error('❌ Error saving JSON file:', writeError.message);
      }
    } else {
      console.log('No patients found since January 1, 2025');

      // Still save an empty result file
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `patients-2025-empty-${timestamp}.json`;
      const filepath = path.join(__dirname, 'data', filename);

      const outputData = {
        fetchDate: new Date().toISOString(),
        sinceDate: since,
        totalPatients: 0,
        totalPages: pageCount,
        patients: []
      };

      try {
        fs.writeFileSync(filepath, JSON.stringify(outputData, null, 2));
        console.log(`\n💾 Empty result saved to: ${filename}`);
      } catch (writeError) {
        console.error('❌ Error saving JSON file:', writeError.message);
      }
    }
    
  } catch (error) {
    console.error('❌ Error fetching patients:');
    console.error('Status:', error.response?.status);
    console.error('Status Text:', error.response?.statusText);
    console.error('Error Data:', error.response?.data);
    console.error('Error Message:', error.message);
    
    if (error.response?.status === 401) {
      console.error('\n🔑 Authentication failed. Please check your access token.');
    } else if (error.response?.status === 403) {
      console.error('\n🚫 Permission denied. Please check your API scopes.');
    } else if (error.response?.status === 429) {
      console.error('\n⏰ Rate limit exceeded. Please wait and try again.');
    }
  }
}

// Run the test
fetchPatientsFrom2025();
